#!/bin/bash

# 设置目录
output_dir="qiime2_output"
merged_dir="$output_dir/merged_replicates"
mkdir -p $merged_dir

# 检查OTU表是否存在
if [ ! -f "$output_dir/table-dn-97.qza" ]; then
    echo "错误: $output_dir/table-dn-97.qza 文件不存在！请先完成OTU聚类"
    exit 1
fi

# 检查metadata文件是否存在
metadata_file="metadata.tsv"
if [ ! -f "$metadata_file" ]; then
    echo "错误: metadata.tsv文件不存在！需要元数据文件来确定平行样本"
    exit 1
fi

echo "开始平行样本合并流程..."

# 步骤1：导出原始OTU表为biom和tsv格式
echo "1. 导出原始OTU表..."
if [ ! -f "$output_dir/exported_otu_table/feature-table.biom" ]; then
    qiime tools export \
      --input-path $output_dir/table-dn-97.qza \
      --output-path $output_dir/exported_otu_table
fi

# 步骤2：创建简单的样本映射文件
echo "2. 创建平行样本合并映射..."
python3 -c '
import pandas as pd
import re
import os

def extract_sample_core(sample_id):
    """从样本ID中提取核心部分，去除平行重复标识符"""
    # 假设平行样本格式为"SXYYz"，其中X是地点代码，YY是月份，z是平行重复标识符(a,b,c)
    # 例如：SB9a, SB9b, SB9c -> SB9
    match = re.match(r"(S[BIP]\d+)[abc]", sample_id)
    if match:
        return match.group(1)
    return sample_id

try:
    # 读取metadata文件
    metadata = pd.read_csv("'"$metadata_file"'", sep="\t")
    
    # 检查是否有sample-id列
    if "sample-id" not in metadata.columns:
        raise ValueError("metadata文件中缺少sample-id列")
    
    # 创建简单的两列映射文件
    mapping_data = []
    for sample_id in metadata["sample-id"]:
        group_id = extract_sample_core(sample_id)
        mapping_data.append({"sample-id": sample_id, "group-id": group_id})
    
    # 保存为TSV文件
    mapping_df = pd.DataFrame(mapping_data)
    mapping_df.to_csv("'"$merged_dir"'/sample_mapping.tsv", sep="\t", index=False)
    
    # 创建更新的metadata文件 - 一行一个合并后的样本
    merged_metadata = []
    # 按组ID分组
    grouped = mapping_df.groupby("group-id")["sample-id"].apply(list).reset_index()
    
    for _, row in grouped.iterrows():
        group_id = row["group-id"]
        samples = row["sample-id"]
        # 使用第一个样本的元数据作为合并后样本的元数据
        sample_row = metadata[metadata["sample-id"] == samples[0]].iloc[0].to_dict()
        sample_row["sample-id"] = group_id  # 用组ID替换样本ID
        sample_row["replicate"] = "merged"  # 更新replicate信息
        merged_metadata.append(sample_row)
    
    # 保存合并后的metadata
    merged_metadata_df = pd.DataFrame(merged_metadata)
    merged_metadata_df.to_csv("'"$merged_dir"'/merged_metadata.tsv", sep="\t", index=False)
    
    print(f"已创建平行样本映射文件: {len(grouped)}个合并样本")
    
except Exception as e:
    print(f"创建平行样本合并映射时出错: {e}")
    exit(1)
'

echo "映射文件示例:"
head -n 5 $merged_dir/sample_mapping.tsv

# 步骤3：使用QIIME2的集合功能合并样本（使用双列映射文件）
echo "3. 合并平行样本..."

# 使用映射文件进行样本合并
qiime feature-table group \
  --i-table $output_dir/table-dn-97.qza \
  --m-metadata-file $merged_dir/sample_mapping.tsv \
  --m-metadata-column group-id \
  --p-axis sample \
  --p-mode sum \
  --o-grouped-table $merged_dir/table-merged.qza

# 步骤4：导出合并后的OTU表
echo "4. 导出合并后的OTU表..."
qiime tools export \
  --input-path $merged_dir/table-merged.qza \
  --output-path $merged_dir/exported_table

# 转换为tsv格式
biom convert \
  -i $merged_dir/exported_table/feature-table.biom \
  -o $merged_dir/merged_otu_table.tsv \
  --to-tsv --header-key taxonomy

# 步骤5：生成可视化结果
echo "5. 生成合并后的OTU表可视化结果..."
qiime feature-table summarize \
  --i-table $merged_dir/table-merged.qza \
  --m-sample-metadata-file $merged_dir/merged_metadata.tsv \
  --o-visualization $merged_dir/table-merged.qzv

# 使用共享的rep-seqs
qiime tools import \
  --type 'FeatureData[Sequence]' \
  --input-path $output_dir/exported_otu_rep_seqs/dna-sequences.fasta \
  --output-path $merged_dir/rep-seqs-merged.qza

echo "平行样本合并完成！"
echo "合并后的OTU表已保存至: $merged_dir/merged_otu_table.tsv"
echo "合并后的元数据文件已保存至: $merged_dir/merged_metadata.tsv"
echo "您现在可以使用合并后的数据进行多样性分析"
