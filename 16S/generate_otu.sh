#!/bin/bash

# 设置输出目录
output_dir="qiime2_output"
mkdir -p $output_dir

# 检查demux.qza文件是否存在
if [ ! -f "$output_dir/demux.qza" ]; then
    echo "错误: $output_dir/demux.qza 文件不存在！请先导入数据"
    exit 1
fi

# 检查metadata文件是否存在
metadata_file="metadata.tsv"
if [ ! -f "$metadata_file" ]; then
    echo "警告: metadata.tsv文件不存在！可视化将不包含元数据信息"
    metadata_param=""
else
    metadata_param="--m-sample-metadata-file $metadata_file"
fi

echo "开始OTU聚类分析流程..."

# 步骤1：使用vsearch进行去重复
echo "1. 使用vsearch进行序列去重复..."
qiime vsearch dereplicate-sequences \
  --i-sequences $output_dir/demux.qza \
  --o-dereplicated-table $output_dir/table-derep.qza \
  --o-dereplicated-sequences $output_dir/rep-seqs-derep.qza

# 步骤2：使用vsearch进行OTU聚类
echo "2. 使用vsearch进行OTU聚类(97%相似度)..."
qiime vsearch cluster-features-de-novo \
  --i-table $output_dir/table-derep.qza \
  --i-sequences $output_dir/rep-seqs-derep.qza \
  --p-perc-identity 0.97 \
  --o-clustered-table $output_dir/table-dn-97.qza \
  --o-clustered-sequences $output_dir/rep-seqs-dn-97.qza 

# 步骤3：生成OTU表可视化
echo "3. 生成OTU表可视化..."
qiime feature-table summarize \
  --i-table $output_dir/table-dn-97.qza \
  --o-visualization $output_dir/table-dn-97.qzv \
  $metadata_param

qiime feature-table tabulate-seqs \
  --i-data $output_dir/rep-seqs-dn-97.qza \
  --o-visualization $output_dir/rep-seqs-dn-97.qzv

# 步骤4：导出OTU表为可读格式
echo "4. 导出OTU表为biom和tsv格式..."
qiime tools export \
  --input-path $output_dir/table-dn-97.qza \
  --output-path $output_dir/exported_otu_table

# 转换biom格式为tsv格式
biom convert \
  -i $output_dir/exported_otu_table/feature-table.biom \
  -o $output_dir/exported_otu_table/otu-table.tsv \
  --to-tsv

# 步骤5：导出OTU代表序列
echo "5. 导出OTU代表序列..."
qiime tools export \
  --input-path $output_dir/rep-seqs-dn-97.qza \
  --output-path $output_dir/exported_otu_rep_seqs

echo "OTU分析处理完成！"
echo "OTU表已保存至: $output_dir/exported_otu_table/otu-table.tsv"
echo "OTU代表序列已保存至: $output_dir/exported_otu_rep_seqs/dna-sequences.fasta"
echo "可通过以下命令查看OTU可视化结果："
echo "qiime tools view $output_dir/table-dn-97.qzv"
echo "qiime tools view $output_dir/rep-seqs-dn-97.qzv" 