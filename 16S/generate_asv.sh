#!/bin/bash

# 设置输出目录
output_dir="qiime2_output"
mkdir -p $output_dir

# 检查manifest文件是否存在
manifest_file="manifest.tsv"
if [ ! -f "$manifest_file" ]; then
    echo "错误: manifest.tsv文件不存在"
    exit 1
fi

echo "开始处理QIIME2分析流程..."

# 步骤3：使用DADA2进行去噪并生成ASV表
echo "3. 运行DADA2去噪算法，生成ASV表..."
# 根据质量图修改参数：保留前端高质量序列，在质量下降前截断
qiime dada2 denoise-single \
  --i-demultiplexed-seqs $output_dir/demux.qza \
  --p-trim-left 0 \
  --p-trunc-len 440 \
  --p-max-ee 4.0 \
  --p-trunc-q 1 \
  --o-representative-sequences $output_dir/rep-seqs.qza \
  --o-table $output_dir/table.qza \
  --o-denoising-stats $output_dir/stats.qza

# 步骤4：生成可视化文件
echo "4. 生成特征表和代表序列可视化..."
qiime feature-table summarize \
  --i-table $output_dir/table.qza \
  --o-visualization $output_dir/table.qzv \
  --m-sample-metadata-file metadata.tsv

qiime feature-table tabulate-seqs \
  --i-data $output_dir/rep-seqs.qza \
  --o-visualization $output_dir/rep-seqs.qzv

qiime metadata tabulate \
  --m-input-file $output_dir/stats.qza \
  --o-visualization $output_dir/stats.qzv

# 步骤5：导出ASV表为可读格式
echo "5. 导出ASV表为biom和tsv格式..."
qiime tools export \
  --input-path $output_dir/table.qza \
  --output-path $output_dir/exported_table

# 转换biom格式为tsv格式
biom convert \
  -i $output_dir/exported_table/feature-table.biom \
  -o $output_dir/exported_table/feature-table.tsv \
  --to-tsv

echo "6. 导出代表序列..."
qiime tools export \
  --input-path $output_dir/rep-seqs.qza \
  --output-path $output_dir/exported_rep_seqs

echo "处理完成！"
echo "ASV表已保存至: $output_dir/exported_table/feature-table.tsv"
echo "代表序列已保存至: $output_dir/exported_rep_seqs/dna-sequences.fasta"
echo "可通过以下命令查看可视化结果："
echo "qiime tools view $output_dir/table.qzv"
echo "qiime tools view $output_dir/rep-seqs.qzv"
