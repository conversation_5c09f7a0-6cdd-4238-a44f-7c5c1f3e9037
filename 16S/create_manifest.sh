#!/bin/bash

# 创建 manifest 文件（TSV 格式）
echo "创建 manifest 文件（制表符分隔）..."
echo -e "sample-id\tabsolute-filepath\tdirection" > manifest.tsv

# 遍历所有 fastq.gz 文件
for file in *338F_806R.fastq.gz; do
  # 提取样本 ID（去掉.338F_806R.fastq.gz 部分）
  sample=$(echo "$file" | sed 's/\.338F_806R\.fastq\.gz//')

  # 添加到 manifest 文件（以制表符分隔）
  echo -e "$sample\t$PWD/$file\tforward" >> manifest.tsv
done

echo "manifest 文件创建完成: manifest.tsv"
cat manifest.tsv