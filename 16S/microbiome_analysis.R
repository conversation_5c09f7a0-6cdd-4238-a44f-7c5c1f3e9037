#!/usr/bin/env Rscript

# 微生物组数据全面分析脚本
# 包括Alpha多样性、Beta多样性、降维和分类组成分析

# 设置工作目录（请修改为您的项目目录）
setwd("/Users/<USER>/Desktop")

# 安装必要的包（如果尚未安装）
if (!requireNamespace("BiocManager", quietly = TRUE))
  install.packages("BiocManager")

# 安装分析所需的包
required_packages <- c(
  "phyloseq", "vegan", "ggplot2", "DESeq2", "dplyr", 
  "tibble", "tidyr", "reshape2", "ape", "microbiome",
  "ggpubr", "patchwork", "ANCOMBC", "lefser", "randomForest"
)

# 安装未安装的包
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    if (pkg %in% c("phyloseq", "DESeq2", "ANCOMBC", "microbiome")) {
      BiocManager::install(pkg)
    } else {
      install.packages(pkg)
    }
  }
}

# 加载库
library(phyloseq)
library(vegan)
library(ggplot2)
library(DESeq2)
library(dplyr)
library(tibble)
library(tidyr)
library(reshape2)
library(ape)
library(microbiome)
library(ggpubr)
library(patchwork)
library(randomForest)


# 设置主题
theme_set(theme_bw())

# 设置输出目录
output_dir <- "R_analysis_results"
dir.create(output_dir, showWarnings = FALSE, recursive = TRUE)

message("开始数据导入和预处理...")

############################################
# 数据导入和预处理
############################################

# 读取OTU表（从QIIME2导出的合并后表格）
otu_table_file <- "/Users/<USER>/Desktop/qiime2_output/exported_otu_table/otu-table.tsv"
otu_data <- read.table(otu_table_file, header = TRUE, sep = "\t", row.names = 1, 
                       check.names = FALSE, comment.char = "", skip = 0)

# 读取元数据
metadata_file <- "/Users/<USER>/Desktop/qiime2_output/metadata.tsv"
metadata <- read.table(metadata_file, header = TRUE, sep = "\t", 
                       row.names = 1, check.names = FALSE, comment.char = "")

# 读取系统发育树（如果有）
tree_file <- "qiime2_output/merged_replicates/diversity/rooted-tree.qza"
if (file.exists(tree_file)) {
  message("导入系统发育树...")
  # 解压qza文件（如果qza是zip格式）
  temp_dir <- tempdir()
  unzip(tree_file, exdir = temp_dir)
  tree_nwk <- list.files(path = file.path(temp_dir, "*/data"), 
                        pattern = ".nwk", full.names = TRUE)[1]
  if (!is.na(tree_nwk)) {
    tree <- ape::read.tree(tree_nwk)
  } else {
    message("未找到Newick格式树文件，将不使用系统发育树")
    tree <- NULL
  }
} else {
  message("未找到系统发育树文件，将不使用系统发育树")
  tree <- NULL
}

# 读取物种注释（如果有）
taxonomy_file <- "/Users/<USER>/Desktop/qiime2_output/taxonomy/exported_taxonomy/taxonomy.tsv"
if (file.exists(taxonomy_file)) {
  message("导入物种分类信息...")
  tax_data <- read.table(taxonomy_file, header = TRUE, sep = "\t", 
                         row.names = 1, check.names = FALSE)
  
  # 处理物种分类列
  if ("Taxon" %in% colnames(tax_data)) {
    # 分割物种分类信息
    tax_split <- strsplit(as.character(tax_data$Taxon), "; ")
    tax_mat <- matrix(NA, nrow = length(tax_split), ncol = 7)
    colnames(tax_mat) <- c("Kingdom", "Phylum", "Class", "Order", "Family", "Genus", "Species")
    
    for (i in 1:length(tax_split)) {
      tax_vec <- tax_split[[i]]
      # 提取每个分类级别
      for (j in 1:length(tax_vec)) {
        if (j <= ncol(tax_mat)) {
          tax_mat[i, j] <- gsub("^[kpcofgs]__", "", tax_vec[j])
        }
      }
    }
    
    # 创建分类矩阵
    taxonomy <- tax_mat
    rownames(taxonomy) <- rownames(tax_data)
  } else {
    message("物种分类文件格式不符合预期，将不使用物种注释")
    taxonomy <- NULL
  }
} else {
  message("未找到物种分类文件，将不使用物种注释")
  taxonomy <- NULL
}

# 创建phyloseq对象
OTU <- otu_table(as.matrix(otu_data), taxa_are_rows = TRUE)
META <- sample_data(metadata)

if (!is.null(taxonomy)) {
  TAX <- tax_table(taxonomy)
  ps <- phyloseq(OTU, TAX, META)
} else {
  ps <- phyloseq(OTU, META)
}

# 检查数据
print(ps)

# 数据过滤 - 移除低丰度特征
ps_filtered <- prune_taxa(taxa_sums(ps) > 10, ps)  # 只保留总计数>10的特征
message(paste0("过滤后保留 ", ntaxa(ps_filtered), " 个特征，占原始特征的 ", 
             round(ntaxa(ps_filtered)/ntaxa(ps)*100, 2), "%"))

# 样本稀疏化处理
# 查找最小测序深度
min_depth <- min(sample_sums(ps_filtered))
message(paste0("最小测序深度为: ", min_depth))

# 稀疏化到最小测序深度
ps_rarefied <- rarefy_even_depth(ps_filtered, sample.size = min_depth, 
                               rngseed = 123, replace = FALSE, trimOTUs = TRUE)

############################################
# 1. Alpha多样性分析
############################################

message("进行Alpha多样性分析...")

# 计算alpha多样性指标
alpha_div <- estimate_richness(ps_filtered,
                               measures = c("Observed", "Chao1", "Shannon", "Simpson"))
# 添加元数据
alpha_div_meta <- cbind(alpha_div, sample_data(ps_filtered))

# 将 month 转为因子（分类变量）
alpha_div_meta$month <- factor(alpha_div_meta$month, 
                               levels = as.character(9:11),  # 若是1–12的数字
                               labels = c("Sep", "Oct", "Nov"))

# 将数据转换为长格式用于绘图
alpha_div_long <- alpha_div_meta %>%
  rownames_to_column("sample_id") %>%
  gather(key = "metric", value = "value", Observed, Shannon, Simpson, Chao1)

# 创建可视化文件夹
dir.create(file.path(output_dir, "alpha_diversity"), showWarnings = FALSE)

# 按location分组的Alpha多样性箱线图
p1 <- ggplot(alpha_div_long, aes(x = location, y = value, fill = location)) + 
  geom_boxplot(alpha = 0.7) +
  geom_jitter(width = 0.2) +
  facet_wrap(~metric, scales = "free_y") +
  labs(title = "各地点间Alpha多样性比较") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

month_comparisons <- list(c("Sep", "Oct"), c("Sep", "Nov"), c("Oct", "Nov"))
p2 <- ggplot(alpha_div_long, aes(x = month, y = value, fill = month)) +
  geom_boxplot(alpha = 0.7, outlier.shape = NA) +
  geom_jitter(width = 0.2, size = 1) +
  facet_grid(metric ~ location, scales = "free_y") +
  labs(title = "同一地点不同月份的Alpha多样性比较",
       x = "Month",
       y = "Alpha diversity index") +
  theme_bw() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1),
        strip.background = element_rect(fill = "gray90"),
        legend.position = "none") +
  stat_compare_means(comparisons = month_comparisons,
                     method = "t.test",
                     label = "p.signif")  # 或使用 "p.format"

ggsave(file.path(output_dir, "alpha_diversity", "alpha_diversity_by_location1.pdf"), 
       p1, width = 10, height = 8)
ggsave(file.path(output_dir, "alpha_diversity", "alpha_diversity_by_location2.pdf"), 
       p2, width = 10, height = 8)

# 按month分组的Alpha多样性箱线图
p3 <- ggplot(alpha_div_long, aes(x = month, y = value, fill = month)) + 
  geom_boxplot(alpha = 0.7) +
  geom_jitter(width = 0.2) +
  facet_wrap(~metric, scales = "free_y") +
  labs(title = "各月份间Alpha多样性比较") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

location_comparisons <- list(c("Business", "Industry"), c("Business", "Park"), c("Industry", "Park"))
p4 <- ggplot(alpha_div_long, aes(x = location, y = value, fill = location)) +
  geom_boxplot(alpha = 0.7, outlier.shape = NA) +
  geom_jitter(width = 0.2, size = 1) +
  facet_grid(metric ~ month, scales = "free_y") +
  labs(title = "同一地点不同月份的Alpha多样性比较",
       x = "Location",
       y = "Alpha diversity index") +
  theme_bw() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1),
        strip.background = element_rect(fill = "gray90"),
        legend.position = "none") +
  stat_compare_means(comparisons = location_comparisons,
                     method = "t.test",
                     label = "p.signif")

ggsave(file.path(output_dir, "alpha_diversity", "alpha_diversity_by_month1.pdf"), 
       p3, width = 10, height = 8)
ggsave(file.path(output_dir, "alpha_diversity", "alpha_diversity_by_month2.pdf"), 
       p4, width = 10, height = 8)

# 保存Alpha多样性数据
write.csv(alpha_div_meta, file.path(output_dir, "alpha_diversity", "alpha_diversity_metrics1.csv"))

# 统计检验 - ANOVA和Tukey HSD检验
alpha_stats <- list()

for (metric in c("Observed", "Shannon", "Simpson", "Chao1")) {
  # 按location分组进行ANOVA
  formula_loc <- as.formula(paste(metric, "~ location"))
  aov_loc <- aov(formula_loc, data = alpha_div_meta)
  
  # 按month分组进行ANOVA
  formula_month <- as.formula(paste(metric, "~ month"))
  aov_month <- aov(formula_month, data = alpha_div_meta)
  
  # Tukey HSD检验
  tukey_loc <- TukeyHSD(aov_loc)
  tukey_month <- TukeyHSD(aov_month)
  
  # 保存结果
  alpha_stats[[paste0(metric, "_location_anova")]] <- summary(aov_loc)
  alpha_stats[[paste0(metric, "_location_tukey")]] <- tukey_loc
  alpha_stats[[paste0(metric, "_month_anova")]] <- summary(aov_month)
  alpha_stats[[paste0(metric, "_month_tukey")]] <- tukey_month
}

# 保存统计结果
saveRDS(alpha_stats, file.path(output_dir, "alpha_diversity", "alpha_diversity_stats.rds"))

############################################
# 2. Beta多样性分析
############################################

message("进行Beta多样性分析...")

# 创建可视化输出文件夹
dir.create(file.path(output_dir, "beta_diversity"), showWarnings = FALSE)

# 定义距离方法
dist_methods <- c("bray", "jaccard", "unifrac", "wunifrac")
dist_matrices <- list()

# 如果没有树，设为空
if (!exists("tree")) tree <- NULL

# 计算距离矩阵
for (method in dist_methods) {
  if (method %in% c("unifrac", "wunifrac") && is.null(tree)) {
    message(paste0("跳过 ", method, " 距离计算，因为没有提供系统发育树"))
    next
  }
  
  message(paste0("计算 ", method, " 距离矩阵..."))
  dist_matrices[[method]] <- phyloseq::distance(ps_filtered, method = method)
}

# 转换样本数据为 data.frame
metadata <- as(sample_data(ps_filtered), "data.frame")

# 进行 PERMANOVA 分析
adonis_results <- list()

for (method in names(dist_matrices)) {
  message(paste0("正在进行 ", method, " PERMANOVA..."))
  
  adonis_loc <- vegan::adonis2(dist_matrices[[method]] ~ location, data = metadata)
  adonis_month <- vegan::adonis2(dist_matrices[[method]] ~ month, data = metadata)
  adonis_interaction <- vegan::adonis2(dist_matrices[[method]] ~ location * month, data = metadata)
  
  adonis_results[[paste0(method, "_location")]] <- adonis_loc
  adonis_results[[paste0(method, "_month")]] <- adonis_month
  adonis_results[[paste0(method, "_interaction")]] <- adonis_interaction
}

# 可保存结果
saveRDS(adonis_results, file.path(output_dir, "beta_diversity", "adonis_results.rds"))


# 可视化Beta多样性结果
pdf(file.path(output_dir, "beta_diversity", "beta_diversity_ordinations.pdf"), 
    width = 12, height = 10)

for (method in names(dist_matrices)) {
  # PCoA降维
  pcoa_result <- cmdscale(dist_matrices[[method]], k = 3, eig = TRUE)
  points <- pcoa_result$points
  eig <- pcoa_result$eig
  percent_explained <- 100 * eig / sum(eig)
  
  # 创建数据框用于绘图
  df <- data.frame(PC1 = points[,1], PC2 = points[,2], PC3 = points[,3])
  df <- cbind(df, sample_data(ps_filtered))
  
  # 按location绘制
  p1 <- ggplot(df, aes(x = PC1, y = PC2, color = location, shape = month)) +
    geom_point(size = 3, alpha = 0.8) +
    labs(title = paste0(method, " 距离 PCoA - 按地点"),
        x = paste0("PC1 (", round(percent_explained[1], 1), "%)"),
        y = paste0("PC2 (", round(percent_explained[2], 1), "%)")) +
    stat_ellipse(aes(group = location), type = "norm")
  
  # 按month绘制
  p2 <- ggplot(df, aes(x = PC1, y = PC2, color = month, shape = location)) +
    geom_point(size = 3, alpha = 0.8) +
    labs(title = paste0(method, " 距离 PCoA - 按月份"),
        x = paste0("PC1 (", round(percent_explained[1], 1), "%)"),
        y = paste0("PC2 (", round(percent_explained[2], 1), "%)")) +
    stat_ellipse(aes(group = month), type = "norm")
  
  # 绘制到PDF
  print(p1)
  print(p2)
  
  # 保存单独的文件
  ggsave(file.path(output_dir, "beta_diversity", paste0("pcoa_", method, "_location.pdf")), 
         p1, width = 8, height = 6)
  ggsave(file.path(output_dir, "beta_diversity", paste0("pcoa_", method, "_month.pdf")), 
         p2, width = 8, height = 6)
}

dev.off()

############################################
# 3. PCA/PCoA/NMDS降维分析
############################################

message("进行降维分析...")

# 创建可视化文件夹
dir.create(file.path(output_dir, "ordination"), showWarnings = FALSE)

# PCA分析
otu_matrix <- t(otu_table(ps_filtered))
pca_result <- prcomp(otu_matrix, scale. = TRUE)
variance_explained <- pca_result$sdev^2 / sum(pca_result$sdev^2) * 100

# 创建数据框用于绘图
pca_df <- data.frame(PC1 = pca_result$x[,1], PC2 = pca_result$x[,2])
pca_df <- cbind(pca_df, sample_data(ps_filtered))

# PCA可视化
p_pca <- ggplot(pca_df, aes(x = PC1, y = PC2)) +
  geom_point(aes(color = location, shape = month), size = 3, alpha = 0.8) +
  labs(title = "PCA Ordination",
      x = paste0("PC1 (", round(variance_explained[1], 1), "%)"),
      y = paste0("PC2 (", round(variance_explained[2], 1), "%)")) +
  stat_ellipse(aes(color = location))

ggsave(file.path(output_dir, "ordination", "pca_plot.pdf"), p_pca, width = 8, height = 6)

# NMDS分析（使用Bray-Curtis距离）
nmds_result <- vegan::metaMDS(vegdist(t(otu_table(ps_filtered)), method = "bray"))
nmds_df <- as.data.frame(nmds_result$points)
colnames(nmds_df) <- c("NMDS1", "NMDS2")
nmds_df <- cbind(nmds_df, sample_data(ps_filtered))

# NMDS可视化
p_nmds <- ggplot(nmds_df, aes(x = NMDS1, y = NMDS2)) +
  geom_point(aes(color = location, shape = month), size = 3, alpha = 0.8) +
  labs(title = "NMDS Ordination (Bray-Curtis distance)",
      x = "NMDS1", y = "NMDS2") +
  stat_ellipse(aes(color = location))

ggsave(file.path(output_dir, "ordination", "nmds_plot.pdf"), p_nmds, width = 8, height = 6)

############################################
# 4. 差异丰度分析
############################################

message("进行差异丰度分析...")

# 创建可视化文件夹
dir.create(file.path(output_dir, "differential_abundance"), showWarnings = FALSE)

# 使用DESeq2进行差异丰度分析
# 转换为DESeq2对象
ps_deseq <- phyloseq_to_deseq2(ps_filtered, ~ location + month)

# 估计大小因子和离散度
ps_deseq <- estimateSizeFactors(ps_deseq)
ps_deseq <- estimateDispersions(ps_deseq)

# 进行差异丰度分析 - 按location
dds_location <- DESeq(ps_deseq, test="Wald", fitType="parametric")

# 获取所有成对比较的结果
location_levels <- unique(sample_data(ps_filtered)$location)
deseq_results_location <- list()

for (i in 1:(length(location_levels)-1)) {
  for (j in (i+1):length(location_levels)) {
    loc1 <- location_levels[i]
    loc2 <- location_levels[j]
    
    # 获取比较结果
    comparison_name <- paste0("location_", loc1, "_vs_", loc2)
    results_obj <- results(dds_location, contrast=c("location", loc1, loc2))
    
    # 筛选显著差异特征
    sig_features <- results_obj[which(results_obj$padj < 0.05), ]
    
    # 保存结果
    deseq_results_location[[comparison_name]] <- sig_features
    
    # 保存为CSV
    if (nrow(sig_features) > 0) {
      sig_df <- as.data.frame(sig_features)
      sig_df$feature_id <- rownames(sig_df)
      
      # 如果有物种分类数据，添加物种信息
      if (!is.null(taxonomy)) {
        tax_info <- as.data.frame(tax_table(ps_filtered))
        sig_df <- merge(sig_df, tax_info, by.x = "feature_id", by.y = "row.names", all.x = TRUE)
      }
      
      write.csv(sig_df, file.path(output_dir, "differential_abundance", 
                                paste0("deseq2_", comparison_name, ".csv")))
      
      # 创建火山图
      volcano_plot <- ggplot(as.data.frame(results_obj), 
                           aes(x = log2FoldChange, y = -log10(padj))) +
        geom_point(aes(color = padj < 0.05), alpha = 0.6) +
        scale_color_manual(values = c("grey", "red")) +
        labs(title = paste("DESeq2:", loc1, "vs", loc2),
            x = "Log2 Fold Change", y = "-Log10 Adjusted P-value") +
        theme(legend.position = "none") +
        geom_vline(xintercept = c(-1, 1), linetype = "dashed") +
        geom_hline(yintercept = -log10(0.05), linetype = "dashed")
      
      ggsave(file.path(output_dir, "differential_abundance", 
                     paste0("volcano_", comparison_name, ".pdf")), 
           volcano_plot, width = 7, height = 6)
    }
  }
}

# 按月份进行差异丰度分析
month_levels <- unique(sample_data(ps_filtered)$month)
deseq_results_month <- list()

for (i in 1:(length(month_levels)-1)) {
  for (j in (i+1):length(month_levels)) {
    m1 <- month_levels[i]
    m2 <- month_levels[j]
    
    # 获取比较结果
    comparison_name <- paste0("month_", m1, "_vs_", m2)
    results_obj <- results(dds_location, contrast=c("month", m1, m2))
    
    # 筛选显著差异特征
    sig_features <- results_obj[which(results_obj$padj < 0.05), ]
    
    # 保存结果
    deseq_results_month[[comparison_name]] <- sig_features
    
    # 保存为CSV
    if (nrow(sig_features) > 0) {
      sig_df <- as.data.frame(sig_features)
      sig_df$feature_id <- rownames(sig_df)
      
      # 如果有物种分类数据，添加物种信息
      if (!is.null(taxonomy)) {
        tax_info <- as.data.frame(tax_table(ps_filtered))
        sig_df <- merge(sig_df, tax_info, by.x = "feature_id", by.y = "row.names", all.x = TRUE)
      }
      
      write.csv(sig_df, file.path(output_dir, "differential_abundance", 
                                paste0("deseq2_", comparison_name, ".csv")))
    }
  }
}

# 保存所有DESeq2结果
saveRDS(list(location = deseq_results_location, month = deseq_results_month), 
       file.path(output_dir, "differential_abundance", "deseq2_results.rds"))

############################################
# 5. 物种组成分析
############################################

message("进行物种组成分析...")

# 创建可视化文件夹
dir.create(file.path(output_dir, "taxonomy_composition"), showWarnings = FALSE)

library(RColorBrewer)

# 检查是否有分类信息
if (!is.null(taxonomy)) {
  # 相对丰度转换
  ps_rel <- transform_sample_counts(ps_filtered, function(x) x / sum(x) * 100)
  
  ## ===== 门水平处理 ===== ##
  ps_phylum <- tax_glom(ps_rel, taxrank = "Phylum")
  phylum_abundance <- psmelt(ps_phylum)
  colnames(phylum_abundance)[colnames(phylum_abundance) == "Abundance"] <- "Relative_Abundance"
  
  # 获取前8个最丰门
  top_phyla <- phylum_abundance %>%
    group_by(Phylum) %>%
    summarize(mean_abundance = mean(Relative_Abundance)) %>%
    arrange(desc(mean_abundance)) %>%
    slice_max(mean_abundance, n = 8) %>%
    pull(Phylum)
  
  # 生成颜色
  phylum_colors <- brewer.pal(8, "Dark2")
  phylum_colors <- setNames(phylum_colors, top_phyla)
  phylum_colors["Others"] <- "gray70"
  
  # 合并少数门为Others
  phylum_abundance$Phylum <- as.character(phylum_abundance$Phylum)
  phylum_abundance$Phylum[!(phylum_abundance$Phylum %in% top_phyla)] <- "Others"
  phylum_abundance$Phylum <- factor(phylum_abundance$Phylum, levels = c(top_phyla, "Others"))
  
  # 绘图（门 - 地点）
  p_phylum_location <- ggplot(phylum_abundance, aes(x = Sample, y = Relative_Abundance, fill = Phylum)) +
    geom_bar(stat = "identity") +
    facet_wrap(~ location, scales = "free_x") +
    labs(title = "门水平物种组成（按地点）", y = "相对丰度 (%)") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
    scale_fill_manual(values = phylum_colors)
  
  ggsave(file.path(output_dir, "taxonomy_composition", "phylum_composition_by_location.pdf"),
         p_phylum_location, width = 12, height = 8)
  
  # 绘图（门 - 月份）
  p_phylum_month <- ggplot(phylum_abundance, aes(x = Sample, y = Relative_Abundance, fill = Phylum)) +
    geom_bar(stat = "identity") +
    facet_wrap(~ month, scales = "free_x") +
    labs(title = "门水平物种组成（按月份）", y = "相对丰度 (%)") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
    scale_fill_manual(values = phylum_colors)
  
  ggsave(file.path(output_dir, "taxonomy_composition", "phylum_composition_by_month.pdf"),
         p_phylum_month, width = 12, height = 8)
  
  ## ===== 属水平处理 ===== ##
  ps_genus <- tax_glom(ps_rel, taxrank = "Genus")
  genus_abundance <- psmelt(ps_genus)
  colnames(genus_abundance)[colnames(genus_abundance) == "Abundance"] <- "Relative_Abundance"
  
  # 获取前8个属
  top_genera <- genus_abundance %>%
    group_by(Genus) %>%
    summarize(mean_abundance = mean(Relative_Abundance)) %>%
    arrange(desc(mean_abundance)) %>%
    slice_max(mean_abundance, n = 11) %>%
    pull(Genus)
  
  # 设置颜色
  genus_colors <- brewer.pal(11, "Spectral")
  genus_colors <- setNames(genus_colors, top_genera)
  genus_colors["Others"] <- "gray70"
  
  # 合并其他属为Others
  genus_abundance$Genus <- as.character(genus_abundance$Genus)
  genus_abundance$Genus[!(genus_abundance$Genus %in% top_genera)] <- "Others"
  genus_abundance$Genus <- factor(genus_abundance$Genus, levels = c(top_genera, "Others"))
  
  # 绘图（属 - 地点）
  p_genus_location <- ggplot(genus_abundance, aes(x = Sample, y = Relative_Abundance, fill = Genus)) +
    geom_bar(stat = "identity") +
    facet_wrap(~ location, scales = "free_x") +
    labs(title = "属水平物种组成（按地点）", y = "相对丰度 (%)") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
    scale_fill_manual(values = genus_colors)
  
  ggsave(file.path(output_dir, "taxonomy_composition", "genus_composition_by_location.pdf"),
         p_genus_location, width = 14, height = 8)
  
  # 绘图（属 - 月份）
  p_genus_month <- ggplot(genus_abundance, aes(x = Sample, y = Relative_Abundance, fill = Genus)) +
    geom_bar(stat = "identity") +
    facet_wrap(~ month, scales = "free_x") +
    labs(title = "属水平物种组成（按月份）", y = "相对丰度 (%)") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
    scale_fill_manual(values = genus_colors)
  
  ggsave(file.path(output_dir, "taxonomy_composition", "genus_composition_by_month.pdf"),
         p_genus_month, width = 14, height = 8)
  
  ## ===== 平均丰度保存与导出 ===== ##
  phylum_avg_location <- phylum_abundance %>%
    group_by(location, Phylum) %>%
    summarize(mean_abundance = mean(Relative_Abundance), .groups = "drop")
  
  # 保存原始堆积图数据
  write.csv(phylum_abundance, file.path(output_dir, "taxonomy_composition", "phylum_abundance.csv"), row.names = FALSE)
  write.csv(genus_abundance,  file.path(output_dir, "taxonomy_composition", "genus_abundance.csv"),  row.names = FALSE)
}

