#!/bin/bash

# 创建元数据文件
echo "创建元数据文件..."
echo -e "sample-id\tlocation\tmonth\treplicate\tdescription" > metadata.tsv

# 遍历所有fastq.gz文件
for file in *338F_806R.fastq.gz; do
  # 提取样本ID
  sample=$(echo $file | sed 's/\.338F_806R\.fastq\.gz//')
  
  # 提取位置信息（S后面的字符：B=Business, I=Industry, P=Park）
  location_code=${sample:1:1}
  case $location_code in
    "B") location="Business" ;;
    "I") location="Industry" ;;
    "P") location="Park" ;;
    *) location="Unknown" ;;
  esac
  
  # 提取月份信息（位置后的数字）
  month=${sample:2:1}
  
  # 提取平行样信息（a, b, c）
  replicate=${sample:3:1}
  
  # 添加到元数据文件
  echo -e "$sample\t$location\t$month\t$replicate\t$sample description" >> metadata.tsv
done

echo "元数据文件创建完成: metadata.tsv"
cat metadata.tsv | head -5
