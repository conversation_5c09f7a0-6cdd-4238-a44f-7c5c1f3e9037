#!/bin/bash

# 设置目录
output_dir="qiime2_output"
mkdir -p $output_dir/taxonomy
classifier_dir="classifiers"

echo "开始物种分类注释流程..."

# 检查OTU代表序列是否存在
if [ ! -f "$output_dir/rep-seqs-dn-97.qza" ]; then
    echo "错误: $output_dir/rep-seqs-dn-97.qza 文件不存在！请先运行OTU聚类"
    exit 1
fi

# 步骤1：确定分类器
# 首先检查是否有Silva分类器
silva_classifier="$classifier_dir/silva-138-99-nb-classifier.qza"
if [ ! -f "$silva_classifier" ]; then
    echo "未找到Silva分类器，请先下载:"
    echo "bash download_silva_classifier.sh"
    echo ""
    echo "或者手动指定分类器路径:"
    echo "请输入分类器文件路径 (例如: /path/to/silva-138-99-nb-classifier.qza):"
    read classifier
else
    echo "找到Silva分类器: $silva_classifier"
    classifier=$silva_classifier
    echo "使用此分类器进行注释"
fi

# 检查分类器文件是否存在
if [ ! -f "$classifier" ]; then
    echo "错误: 分类器文件不存在!"
    echo "您可以运行以下命令下载Silva分类器:"
    echo "bash download_silva_classifier.sh"
    exit 1
fi

# 步骤2：进行物种分类学注释
echo "1. 使用分类器对OTU代表序列进行注释..."
qiime feature-classifier classify-sklearn \
  --i-classifier $classifier \
  --i-reads $output_dir/rep-seqs-dn-97.qza \
  --o-classification $output_dir/taxonomy/taxonomy.qza

# 步骤3：生成物种分类可视化
echo "2. 生成物种分类可视化..."
qiime metadata tabulate \
  --m-input-file $output_dir/taxonomy/taxonomy.qza \
  --o-visualization $output_dir/taxonomy/taxonomy.qzv

# 步骤4：生成带有物种信息的特征表可视化
echo "3. 生成带有物种信息的特征表..."

# 检查metadata文件是否存在
metadata_file="metadata.tsv"
if [ ! -f "$metadata_file" ]; then
    echo "警告: metadata.tsv文件不存在！可视化将不包含元数据信息"
    metadata_param=""
else
    # 修复参数名称为新版QIIME2兼容的
    metadata_param="--m-metadata-file $metadata_file"
fi

qiime taxa barplot \
  --i-table $output_dir/table-dn-97.qza \
  --i-taxonomy $output_dir/taxonomy/taxonomy.qza \
  --o-visualization $output_dir/taxonomy/taxa-bar-plots.qzv \
  $metadata_param

# 步骤5：导出物种分类结果
echo "4. 导出物种分类结果..."
qiime tools export \
  --input-path $output_dir/taxonomy/taxonomy.qza \
  --output-path $output_dir/taxonomy/exported_taxonomy

# 确保taxonomy.tsv文件存在并有正确的格式
if [ -f "$output_dir/taxonomy/exported_taxonomy/taxonomy.tsv" ]; then
    # 检查文件是否有头行
    if ! head -n 1 "$output_dir/taxonomy/exported_taxonomy/taxonomy.tsv" | grep -q "Feature ID"; then
        echo "修复taxonomy.tsv文件格式..."
        # 添加头行
        sed -i '1i Feature ID\tTaxon\tConfidence' "$output_dir/taxonomy/exported_taxonomy/taxonomy.tsv"
    fi
else
    echo "错误: $output_dir/taxonomy/exported_taxonomy/taxonomy.tsv 文件不存在！"
    exit 1
fi

# 步骤6：生成带有物种注释的OTU表
echo "5. 生成带有物种注释的OTU表..."

# 导出OTU表
if [ ! -f "$output_dir/exported_otu_table/feature-table.biom" ]; then
    echo "导出OTU表为biom格式..."
    qiime tools export \
      --input-path $output_dir/table-dn-97.qza \
      --output-path $output_dir/exported_otu_table
fi

# 使用更可靠的方式合并物种分类信息
echo "将物种分类信息添加到OTU表中..."

# 首先转换feature-table.biom为tsv格式
biom convert \
  -i $output_dir/exported_otu_table/feature-table.biom \
  -o $output_dir/taxonomy/feature-table.tsv \
  --to-tsv

# 然后手动合并OTU表和物种注释信息
echo "生成带有物种注释的OTU表..."
python3 -c '
import sys
import pandas as pd

# 读取OTU表和物种分类表
try:
    otu_df = pd.read_csv("'"$output_dir"'/taxonomy/feature-table.tsv", sep="\t", skiprows=1)
    otu_df.set_index("#OTU ID", inplace=True)
    
    tax_df = pd.read_csv("'"$output_dir"'/taxonomy/exported_taxonomy/taxonomy.tsv", sep="\t")
    tax_df.set_index("Feature ID", inplace=True)
    
    # 将物种注释添加到OTU表
    merged_df = otu_df.join(tax_df)
    
    # 保存结果
    merged_df.to_csv("'"$output_dir"'/taxonomy/otu_table_with_taxonomy.tsv", sep="\t")
    print("成功创建带有物种注释的OTU表！")
except Exception as e:
    print(f"合并OTU表和物种注释时出错: {e}")
    sys.exit(1)
'

if [ $? -ne 0 ]; then
    echo "错误：无法使用Python合并OTU表和物种注释。"
    echo "尝试使用备用方法..."
    
    # 备用方法：使用简化的shell命令合并文件
    awk 'NR==1{print}' "$output_dir/taxonomy/feature-table.tsv" > "$output_dir/taxonomy/otu_table_with_taxonomy.tsv"
    tail -n +2 "$output_dir/taxonomy/feature-table.tsv" | sort > temp_otu.tsv
    tail -n +2 "$output_dir/taxonomy/exported_taxonomy/taxonomy.tsv" | sort > temp_tax.tsv
    join -t $'\t' -1 1 -2 1 temp_otu.tsv temp_tax.tsv >> "$output_dir/taxonomy/otu_table_with_taxonomy.tsv"
    rm temp_otu.tsv temp_tax.tsv
fi

echo "物种注释完成！"
echo "物种分类结果已保存至: $output_dir/taxonomy/exported_taxonomy/taxonomy.tsv"
echo "带注释的OTU表已保存至: $output_dir/taxonomy/otu_table_with_taxonomy.tsv"
echo "可通过以下命令查看物种组成可视化结果："
echo "qiime tools view $output_dir/taxonomy/taxa-bar-plots.qzv"
echo "qiime tools view $output_dir/taxonomy/taxonomy.qzv"

echo "接下来可以进行多样性分析、差异分析等后续分析..." 