#!/bin/bash

# 设置目录
output_dir="qiime2_output/merged_replicates"
diversity_dir="$output_dir/diversity"
mkdir -p $diversity_dir

# 检查必要的文件
if [ ! -f "$output_dir/table-merged.qza" ]; then
    echo "错误: $output_dir/table-merged.qza 文件不存在！请先运行合并平行样本的脚本"
    exit 1
fi

# 使用合并后的metadata
metadata_file="$output_dir/merged_metadata.tsv"
if [ ! -f "$metadata_file" ]; then
    echo "错误: $metadata_file 文件不存在！"
    exit 1
fi

echo "开始基于合并平行样本的多样性分析流程..."

# 步骤1：构建系统发育树（从合并后的代表序列）
echo "1. 构建系统发育树..."
qiime phylogeny align-to-tree-mafft-fasttree \
  --i-sequences $output_dir/rep-seqs-merged.qza \
  --o-alignment $diversity_dir/aligned-rep-seqs.qza \
  --o-masked-alignment $diversity_dir/masked-aligned-rep-seqs.qza \
  --o-tree $diversity_dir/unrooted-tree.qza \
  --o-rooted-tree $diversity_dir/rooted-tree.qza

# 步骤2：根据样本序列数生成稀疏曲线
echo "2. 生成稀疏曲线评估测序深度..."
qiime diversity alpha-rarefaction \
  --i-table $output_dir/table-merged.qza \
  --i-phylogeny $diversity_dir/rooted-tree.qza \
  --p-max-depth 150000 \
  --p-steps 30 \
  --m-metadata-file $metadata_file \
  --o-visualization $diversity_dir/alpha-rarefaction.qzv

# 步骤3：根据稀疏曲线确定采样深度
echo "请查看稀疏曲线结果，确定合适的采样深度："
echo "qiime tools view $diversity_dir/alpha-rarefaction.qzv"
echo "根据稀疏曲线，输入合适的采样深度值(默认100000)："
read sampling_depth
if [ -z "$sampling_depth" ]; then
    sampling_depth=100000
    echo "使用默认采样深度: $sampling_depth"
fi

# 步骤4：计算核心多样性指标
echo "4. 计算核心多样性指标（Alpha和Beta多样性）..."
qiime diversity core-metrics-phylogenetic \
  --i-phylogeny $diversity_dir/rooted-tree.qza \
  --i-table $output_dir/table-merged.qza \
  --p-sampling-depth $sampling_depth \
  --m-metadata-file $metadata_file \
  --output-dir $diversity_dir/core-metrics-results

# 步骤5：计算额外的Alpha多样性指标
echo "5. 计算额外的Alpha多样性指标..."
# Shannon指数
qiime diversity alpha \
  --i-table $output_dir/table-merged.qza \
  --p-metric 'shannon' \
  --o-alpha-diversity $diversity_dir/shannon_vector.qza

# Simpson指数
qiime diversity alpha \
  --i-table $output_dir/table-merged.qza \
  --p-metric 'simpson' \
  --o-alpha-diversity $diversity_dir/simpson_vector.qza

# Chao1指数
qiime diversity alpha \
  --i-table $output_dir/table-merged.qza \
  --p-metric 'chao1' \
  --o-alpha-diversity $diversity_dir/chao1_vector.qza

# 步骤6：Alpha多样性统计检验
echo "6. 进行Alpha多样性统计分析..."

# 分析不同分组因素 - 平行样已合并，仅分析location和month
groups=("location" "month")

# 为每个分组因素进行Alpha多样性比较
for metric in "shannon" "simpson" "chao1" "observed_features"; do
    if [ "$metric" = "observed_features" ]; then
        metric_file="$diversity_dir/core-metrics-results/observed_features_vector.qza"
    else
        metric_file="$diversity_dir/${metric}_vector.qza"
    fi
    
    for group in "${groups[@]}"; do
        echo "  分析 $metric 多样性在 $group 组间的差异..."
        qiime diversity alpha-group-significance \
          --i-alpha-diversity $metric_file \
          --m-metadata-file $metadata_file \
          --o-visualization $diversity_dir/alpha-group-significance-${metric}-${group}.qzv
    done
done

# 步骤7：Beta多样性分析和可视化
echo "7. 进行Beta多样性分析和可视化..."

# 为每个分组因素生成Beta多样性可视化
distance_metrics=("bray_curtis" "jaccard" "weighted_unifrac" "unweighted_unifrac")

for metric in "${distance_metrics[@]}"; do
    for group in "${groups[@]}"; do
        echo "  基于 $metric 距离分析 $group 组间的群落差异..."
        qiime diversity beta-group-significance \
          --i-distance-matrix $diversity_dir/core-metrics-results/${metric}_distance_matrix.qza \
          --m-metadata-file $metadata_file \
          --m-metadata-column $group \
          --o-visualization $diversity_dir/beta-group-significance-${metric}-${group}.qzv \
          --p-pairwise
    done
    
    # 生成主坐标分析图(PCoA)
    echo "  生成 $metric 距离的主坐标分析图(PCoA)..."
    for group in "${groups[@]}"; do
        qiime emperor plot \
          --i-pcoa $diversity_dir/core-metrics-results/${metric}_pcoa_results.qza \
          --m-metadata-file $metadata_file \
          --p-custom-axes $group \
          --o-visualization $diversity_dir/${metric}-emperor-${group}.qzv
    done
done

# 步骤8：导出多样性结果
echo "8. 导出多样性分析结果..."

# 导出Alpha多样性表
mkdir -p $diversity_dir/exported
for metric in "shannon" "simpson" "chao1" "observed_features"; do
    if [ "$metric" = "observed_features" ]; then
        metric_file="$diversity_dir/core-metrics-results/observed_features_vector.qza"
    else
        metric_file="$diversity_dir/${metric}_vector.qza"
    fi
    
    qiime tools export \
      --input-path $metric_file \
      --output-path $diversity_dir/exported/${metric}
done

# 步骤9：生成汇总的Alpha多样性表
echo "9. 生成汇总的Alpha多样性表..."
python3 -c '
import pandas as pd
import glob
import os

try:
    # 读取不同的Alpha多样性结果
    diversity_files = glob.glob("'"$diversity_dir"'/exported/*/alpha-diversity.tsv")
    
    dfs = []
    for file in diversity_files:
        metric = os.path.basename(os.path.dirname(file))
        df = pd.read_csv(file, sep="\t")
        df.columns = ["sample_id", metric]
        dfs.append(df)
    
    # 合并所有Alpha多样性指标
    if dfs:
        merged_df = dfs[0]
        for df in dfs[1:]:
            merged_df = pd.merge(merged_df, df, on="sample_id")
        
        # 保存汇总表
        merged_df.to_csv("'"$diversity_dir"'/alpha_diversity_summary.tsv", sep="\t", index=False)
        print("成功创建Alpha多样性汇总表！")
    else:
        print("没有找到任何Alpha多样性结果文件")
except Exception as e:
    print(f"合并Alpha多样性表时出错: {e}")
'

echo "合并平行样本后的多样性分析完成！"
echo "您可以查看以下结果文件和可视化："
echo ""
echo "稀疏曲线："
echo "qiime tools view $diversity_dir/alpha-rarefaction.qzv"
echo ""
echo "Alpha多样性统计结果："
echo "qiime tools view $diversity_dir/alpha-group-significance-*.qzv"
echo ""
echo "Beta多样性PCoA图："
echo "qiime tools view $diversity_dir/*-emperor-*.qzv"
echo ""
echo "Beta多样性统计结果："
echo "qiime tools view $diversity_dir/beta-group-significance-*.qzv"
echo ""
echo "Alpha多样性汇总表："
echo "$diversity_dir/alpha_diversity_summary.tsv"
EOF