#!/bin/bash

# 创建目录用于保存分类器
classifier_dir="classifiers"
mkdir -p $classifier_dir

echo "开始下载Silva分类器..."

# 选择Silva版本
echo "正在下载QIIME2兼容的Silva 138 99% 分类器(适用于V3-V4区域 341F/806R)..."

# 下载预训练的Silva分类器
wget -O $classifier_dir/silva-138-99-nb-classifier.qza "https://data.qiime2.org/2023.5/common/silva-138-99-nb-classifier.qza"

# 检查下载是否成功
if [ -f "$classifier_dir/silva-138-99-nb-classifier.qza" ]; then
    echo "下载完成！分类器已保存至: $classifier_dir/silva-138-99-nb-classifier.qza"
    echo "现在您可以运行物种注释脚本了:"
    echo "bash annotate_taxa.sh"
    echo "提示：当要求输入分类器路径时，请输入: $classifier_dir/silva-138-99-nb-classifier.qza"
else
    echo "下载失败，可能是网络问题或QIIME2数据资源链接已更新。"
    echo "请访问QIIME2官方网站手动下载分类器: https://docs.qiime2.org/2023.5/data-resources/"
    echo "或者尝试以下备用下载方式:"
    echo ""
    echo "方法1. 使用curl下载:"
    echo "curl -L -o $classifier_dir/silva-138-99-nb-classifier.qza \"https://data.qiime2.org/2023.5/common/silva-138-99-nb-classifier.qza\""
    echo ""
    echo "方法2. 浏览器直接访问下载链接:"
    echo "https://data.qiime2.org/2023.5/common/silva-138-99-nb-classifier.qza"
fi

# 提供训练自定义分类器的信息
echo ""
echo "如果您需要针对特定引物对训练自定义分类器，可以参考以下步骤:"
echo "1. 下载Silva参考序列和分类"
echo "2. 提取特定引物区域"
echo "3. 训练分类器"
echo ""
echo "详细教程: https://docs.qiime2.org/2023.5/tutorials/feature-classifier/" 